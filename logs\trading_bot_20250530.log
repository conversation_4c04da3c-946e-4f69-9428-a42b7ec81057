2025-05-30 15:27:59 | TradingBot | INFO | 🔍 Starting analysis for USD/JPY
2025-05-30 15:27:59 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for USD/JPY (Enhanced)
2025-05-30 15:27:59 | MarketData | INFO | 🔍 Fetching comprehensive data for USD/JPY
2025-05-30 15:28:01 | MarketData | INFO | 💰 Quote for USD/JPY: $143.9290
2025-05-30 15:28:02 | MarketData | INFO | 📊 Retrieved 100 intraday data points for USD/JPY
2025-05-30 15:28:03 | MarketData | INFO | 📊 Retrieved 100 daily data points for USD/JPY
2025-05-30 15:28:04 | MarketData | INFO | 📈 Retrieved RSI data for USD/JPY
2025-05-30 15:28:06 | MarketData | INFO | 📈 Retrieved MACD data for USD/JPY
2025-05-30 15:28:07 | MarketData | INFO | 📈 Retrieved EMA data for USD/JPY
2025-05-30 15:28:08 | MarketData | INFO | 📈 Retrieved BBANDS data for USD/JPY
2025-05-30 15:28:08 | TradingBot | INFO | [UP] USD/JPY: $143.93 (+0.00%)
2025-05-30 15:28:08 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/JPY
2025-05-30 15:28:24 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 15:28:24 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/JPY | Confidence: 60.00%
2025-05-30 15:28:24 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a neutral to slightly bearish outlook for USD/JPY. The RSI is at 47.51, indicating neither overbought nor oversold conditions, suggesting a lack of strong directional momentum. The MACD is slightly negative with a histogram value of -0.0168, indicating a weak bearish momentum. The current price is below the EMA of 144.3073, suggesting a bearish trend. However, the price is within the Bollinger Bands, indicating no extreme volatility or breakout. Given these mixed signals and the lack of a strong trend, it is prudent to hold off on entering a new position until clearer signals emerge.
2025-05-30 15:28:24 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 15:29:24 | TradingBot | INFO | 🔍 Starting analysis for USD/JPY
2025-05-30 15:29:24 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for USD/JPY (Enhanced)
2025-05-30 15:29:24 | MarketData | INFO | 🔍 Fetching comprehensive data for USD/JPY
2025-05-30 15:29:25 | MarketData | INFO | 💰 Quote for USD/JPY: $143.9610
2025-05-30 15:29:27 | MarketData | INFO | 📊 Retrieved 100 intraday data points for USD/JPY
2025-05-30 15:29:28 | MarketData | INFO | 📊 Retrieved 100 daily data points for USD/JPY
2025-05-30 15:29:29 | MarketData | INFO | 📈 Retrieved RSI data for USD/JPY
2025-05-30 15:29:30 | MarketData | INFO | 📈 Retrieved MACD data for USD/JPY
2025-05-30 15:29:31 | MarketData | INFO | 📈 Retrieved EMA data for USD/JPY
2025-05-30 15:29:33 | MarketData | INFO | 📈 Retrieved BBANDS data for USD/JPY
2025-05-30 15:29:33 | TradingBot | INFO | [UP] USD/JPY: $143.96 (+0.00%)
2025-05-30 15:29:33 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/JPY
2025-05-30 15:29:44 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 15:29:44 | SignalGenerator | INFO | [SIGNAL] HOLD | USD/JPY | Confidence: 60.00%
2025-05-30 15:29:44 | SignalGenerator | INFO | [REASONING] The current technical indicators and price action suggest a cautious approach. The RSI is neutral at 47.6408, indicating neither overbought nor oversold conditions. The MACD is slightly negative, with a histogram value of -0.0148, suggesting weak bearish momentum. The current price is below the EMA of 144.3104, indicating a potential downtrend. However, the recent price action shows a slight upward momentum with the last close at 143.96100, which is the highest close in the recent periods. The Bollinger Bands indicate the price is near the lower band, suggesting potential support. Given these mixed signals and the absence of strong directional momentum, it is prudent to hold off on entering a new position until clearer signals emerge.
2025-05-30 15:29:44 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 15:29:56 | TradingBot | INFO | [SHUTDOWN] Shutdown signal received, stopping bot...
2025-05-30 15:29:56 | EnhancedMarketData | INFO | 🧹 Enhanced market data provider cleaned up
2025-05-30 15:29:56 | TradingBot | INFO | 🛑 Trading bot stopped
2025-05-30 15:29:56 | EnhancedMarketData | INFO | 🧹 Enhanced market data provider cleaned up
2025-05-30 15:29:56 | TradingBot | INFO | 🛑 Trading bot stopped
2025-05-30 15:45:31 | TradingBot | INFO | 🔍 Starting analysis for AAPL
2025-05-30 15:45:31 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for AAPL (Enhanced)
2025-05-30 15:45:31 | MarketData | INFO | 🔍 Fetching comprehensive data for AAPL
2025-05-30 15:45:33 | MarketData | INFO | 💰 Quote for AAPL: $200.0600
2025-05-30 15:45:34 | MarketData | INFO | 📊 Retrieved 100 intraday data points for AAPL
2025-05-30 15:45:35 | MarketData | INFO | 📊 Retrieved 100 daily data points for AAPL
2025-05-30 15:45:36 | MarketData | INFO | 📈 Retrieved RSI data for AAPL
2025-05-30 15:45:37 | MarketData | INFO | 📈 Retrieved MACD data for AAPL
2025-05-30 15:45:39 | MarketData | INFO | 📈 Retrieved EMA data for AAPL
2025-05-30 15:45:40 | MarketData | INFO | 📈 Retrieved BBANDS data for AAPL
2025-05-30 15:45:40 | TradingBot | INFO | [UP] AAPL: $200.06 (+0.06%)
2025-05-30 15:45:40 | AIAnalyzer | INFO | [AI] Analyzing market data for AAPL
2025-05-30 15:45:55 | AIAnalyzer | INFO | [AI] Analysis complete: HOLD (confidence: 60.00%)
2025-05-30 15:45:55 | SignalGenerator | INFO | [SIGNAL] HOLD | AAPL | Confidence: 60.00%
2025-05-30 15:45:55 | SignalGenerator | INFO | [REASONING] The current technical indicators suggest a cautious approach. The RSI is at 44.65, indicating that the stock is neither overbought nor oversold, suggesting a neutral momentum. The MACD is negative with a histogram below zero, indicating bearish momentum, but it is not strongly negative, which suggests a lack of strong selling pressure. The price is currently below the EMA of 203.28, indicating a short-term downtrend. However, the price is near the middle band of the Bollinger Bands, suggesting potential support around this level. The recent price action shows a gradual upward movement with higher highs and higher lows, but the volume is not significantly increasing, which raises concerns about the strength of the move. Given the lack of strong bullish signals and the current market conditions, it is prudent to hold off on entering a new position until clearer signals emerge.
2025-05-30 15:45:55 | SignalGenerator | INFO | [REJECT] Signal confidence 60.00% below threshold 70.00%
2025-05-30 16:19:53 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:19:53 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:19:53 | MarketData | INFO | 🔍 Fetching comprehensive data for USD/CAD
2025-05-30 16:20:04 | MarketData | ERROR | [ERROR] in Twelve Data API request: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /quote?symbol=USD%2FCAD&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D730D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\socket.py", line 954, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 715, in urlopen
    httplib_response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 1058, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x00000182D7D730D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 799, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /quote?symbol=USD%2FCAD&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D730D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Desktop\FX\market_data.py", line 36, in _make_request
    response = self.session.get(url, params=params, timeout=30)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /quote?symbol=USD%2FCAD&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D730D0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-05-30 16:20:16 | MarketData | ERROR | [ERROR] in Twelve Data API request: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1min&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D735E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\socket.py", line 954, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 715, in urlopen
    httplib_response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 1058, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x00000182D7D735E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 799, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1min&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D735E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Desktop\FX\market_data.py", line 36, in _make_request
    response = self.session.get(url, params=params, timeout=30)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1min&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7D735E0>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-05-30 16:20:29 | MarketData | ERROR | [ERROR] in Twelve Data API request: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1day&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7E2DD30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 174, in _new_conn
    conn = connection.create_connection(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\connection.py", line 72, in create_connection
    for res in socket.getaddrinfo(host, port, family, socket.SOCK_STREAM):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\socket.py", line 954, in getaddrinfo
    for res in _socket.getaddrinfo(host, port, family, type, proto, flags):
socket.gaierror: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 715, in urlopen
    httplib_response = self._make_request(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 404, in _make_request
    self._validate_conn(conn)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 1058, in _validate_conn
    conn.connect()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 363, in connect
    self.sock = conn = self._new_conn()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connection.py", line 186, in _new_conn
    raise NewConnectionError(
urllib3.exceptions.NewConnectionError: <urllib3.connection.HTTPSConnection object at 0x00000182D7E2DD30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 486, in send
    resp = conn.urlopen(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\connectionpool.py", line 799, in urlopen
    retries = retries.increment(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\urllib3\util\retry.py", line 592, in increment
    raise MaxRetryError(_pool, url, error or ResponseError(cause))
urllib3.exceptions.MaxRetryError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1day&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7E2DD30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "E:\Desktop\FX\market_data.py", line 36, in _make_request
    response = self.session.get(url, params=params, timeout=30)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 602, in get
    return self.request("GET", url, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 589, in request
    resp = self.send(prep, **send_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\sessions.py", line 703, in send
    r = adapter.send(request, **kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python39\lib\site-packages\requests\adapters.py", line 519, in send
    raise ConnectionError(e, request=request)
requests.exceptions.ConnectionError: HTTPSConnectionPool(host='api.twelvedata.com', port=443): Max retries exceeded with url: /time_series?symbol=USD%2FCAD&interval=1day&outputsize=100&apikey=86652d916d9a4edc97166fe563183ed9 (Caused by NewConnectionError('<urllib3.connection.HTTPSConnection object at 0x00000182D7E2DD30>: Failed to establish a new connection: [Errno 11001] getaddrinfo failed'))
2025-05-30 16:20:39 | MarketData | INFO | 📈 Retrieved RSI data for USD/CAD
2025-05-30 16:20:40 | MarketData | INFO | 📈 Retrieved MACD data for USD/CAD
2025-05-30 16:20:41 | MarketData | INFO | 📈 Retrieved EMA data for USD/CAD
2025-05-30 16:20:42 | MarketData | INFO | 📈 Retrieved BBANDS data for USD/CAD
2025-05-30 16:20:42 | TradingBot | ERROR | Failed to fetch market data for USD/CAD
2025-05-30 16:21:12 | TradingBot | INFO | Starting analysis for USD/CAD
2025-05-30 16:21:12 | EnhancedMarketData | INFO | 🔍 Fetching comprehensive data for USD/CAD (Enhanced)
2025-05-30 16:21:12 | MarketData | INFO | 🔍 Fetching comprehensive data for USD/CAD
2025-05-30 16:21:13 | MarketData | INFO | 💰 Quote for USD/CAD: $1.3720
2025-05-30 16:21:15 | MarketData | INFO | 📊 Retrieved 100 intraday data points for USD/CAD
2025-05-30 16:21:16 | MarketData | INFO | 📊 Retrieved 100 daily data points for USD/CAD
2025-05-30 16:21:17 | MarketData | INFO | 📈 Retrieved RSI data for USD/CAD
2025-05-30 16:21:18 | MarketData | INFO | 📈 Retrieved MACD data for USD/CAD
2025-05-30 16:21:19 | MarketData | INFO | 📈 Retrieved EMA data for USD/CAD
2025-05-30 16:21:21 | MarketData | INFO | 📈 Retrieved BBANDS data for USD/CAD
2025-05-30 16:21:21 | TradingBot | INFO | [UP] USD/CAD: $1.37 (+0.00%)
2025-05-30 16:21:21 | AIAnalyzer | INFO | [AI] Analyzing market data for USD/CAD
2025-05-30 16:21:34 | AIAnalyzer | INFO | [AI] Analysis complete: BUY (confidence: 70.00%)
2025-05-30 16:21:34 | SignalGenerator | INFO | [STATE_CHANGE] USD/CAD: NO_POSITION → LONG_POSITION
2025-05-30 16:21:34 | SignalGenerator | ERROR | Error exporting single signal: [Errno 2] No such file or directory: 'signal_buy_USD/CAD_20250530_162134.json'
2025-05-30 16:21:34 | SignalGenerator | INFO | [SIGNAL] BUY | USD/CAD | Confidence: 70.00%
2025-05-30 16:21:34 | SignalGenerator | INFO | [REASONING] The USD/CAD pair is currently trading near the lower Bollinger Band, suggesting potential support and a possible reversal. The RSI is at 34.95, indicating the pair is approaching oversold conditions, which could lead to a buying opportunity. Despite the MACD being slightly negative, the histogram is close to zero, suggesting that bearish momentum may be weakening. The recent price action shows consolidation around the 1.37200 level, which could act as a support. Given these technical indicators, a short-term buy position could be considered.
2025-05-30 16:21:34 | SignalGenerator | INFO | [APPROVE] Signal approved for execution: BUY USD/CAD (Confidence: 70.00%)
2025-05-30 16:22:03 | TradingBot | INFO | [SHUTDOWN] Shutdown signal received, stopping bot...
2025-05-30 16:22:03 | SignalGenerator | INFO | [EXPORT] Exported 1 signals to trading_signals_20250530_162203.json
2025-05-30 16:22:03 | EnhancedMarketData | INFO | 🧹 Enhanced market data provider cleaned up
2025-05-30 16:22:03 | TradingBot | INFO | Trading bot stopped
2025-05-30 16:22:03 | SignalGenerator | INFO | [EXPORT] Exported 1 signals to trading_signals_20250530_162203.json
2025-05-30 16:22:03 | EnhancedMarketData | INFO | 🧹 Enhanced market data provider cleaned up
2025-05-30 16:22:03 | TradingBot | INFO | Trading bot stopped
