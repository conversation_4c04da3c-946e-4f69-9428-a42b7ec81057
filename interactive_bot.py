#!/usr/bin/env python3
"""
Interactive Trading Bot Launcher
Simple launcher that runs the trading bot in interactive mode by default
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from trading_bot import TradingBot
from config import Config
from rich.console import Console

def main():
    """Launch the trading bot in interactive mode"""
    console = Console()

    # Display welcome banner
    console.print("\n[bold blue]AI Trading Bot - Interactive Mode[/bold blue]")
    console.print("[green]Welcome to the AI-Powered Trading Analysis Tool![/green]")
    console.print("This bot will help you analyze stocks and FX pairs using AI")

    # Show data source
    console.print("📊 [cyan]Using Twelve Data API for market data[/cyan]")

    console.print("-" * 60)

    # Ask user for analysis mode
    console.print("\n🎯 [bold cyan]Analysis Mode Selection[/bold cyan]")
    console.print("1. Single Analysis - Analyze once and exit")
    console.print("2. Continuous Analysis - Keep analyzing with trade state tracking")

    while True:
        try:
            mode_choice = input("\nSelect mode (1 or 2): ").strip()
            if mode_choice == "1":
                console.print("✅ [green]Single Analysis mode selected[/green]")
                break
            elif mode_choice == "2":
                console.print("✅ [green]Continuous Analysis mode selected[/green]")
                break
            else:
                console.print("❌ [red]Please enter 1 or 2[/red]")
        except KeyboardInterrupt:
            console.print("\n[green]Goodbye! Thanks for using the AI Trading Bot![/green]")
            return

    console.print("-" * 60)

    # Validate configuration
    if not Config.validate_config():
        console.print("[red]Configuration validation failed. Please check your .env file.[/red]")
        console.print("[yellow]Make sure you have set your TWELVE_DATA_API_KEY and OPENAI_API_KEY[/yellow]")
        return

    # Create and run bot in interactive mode
    try:
        bot = TradingBot()
        if mode_choice == "1":
            bot.run_interactive_analysis()
        else:  # mode_choice == "2"
            bot.run_continuous_interactive_analysis()
    except KeyboardInterrupt:
        console.print("\n[green]Goodbye! Thanks for using the AI Trading Bot![/green]")
    except Exception as e:
        console.print(f"\n[red]An error occurred: {e}[/red]")
        console.print("[yellow]Please check your configuration and try again[/yellow]")

if __name__ == "__main__":
    main()
