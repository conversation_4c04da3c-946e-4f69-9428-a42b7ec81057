"""
Main Trading Bot Controller
"""
import time
import schedule
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import signal
import sys
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live

from config import Config
from market_data import MarketDataProvider
from enhanced_market_data import EnhancedMarketDataProvider
from ai_analyzer import AIMarketAnalyzer
from signal_generator import SignalGenerator, TradingSignal, TradeState, SignalType
from logger_config import setup_logger, log_market_data, log_error

class TradingBot:
    """Main trading bot orchestrator"""

    def __init__(self):
        self.logger = setup_logger("TradingBot")
        self.console = Console()

        # Initialize components
        self.market_data_provider = MarketDataProvider()
        self.enhanced_market_data_provider = EnhancedMarketDataProvider()
        self.ai_analyzer = AIMarketAnalyzer()
        self.signal_generator = SignalGenerator()

        # Bot state
        self.is_running = False
        self.analysis_count = 0
        self.last_analysis_time = None

        # Setup graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        self.logger.info("[SHUTDOWN] Shutdown signal received, stopping bot...")
        self.stop()
        sys.exit(0)

    def analyze_symbol(self, symbol: str, interval: str = None, additional_context: str = "") -> Optional[TradingSignal]:
        """
        Perform complete analysis for a single symbol

        Args:
            symbol: Stock symbol or FX pair to analyze
            interval: Time interval for analysis (e.g., '1min', '5min', '1h')
            additional_context: Additional context for AI analysis

        Returns:
            Trading signal or None if analysis failed
        """
        try:
            self.logger.info(f"Starting analysis for {symbol}")

            # Use provided interval or fall back to config
            trading_interval = interval or Config.get_trading_interval()

            # Check connectivity first
            if not self.enhanced_market_data_provider.is_connected():
                self.console.print("[red]ERROR: Cannot connect to market data provider[/red]")
                self.console.print("[yellow]Please check:[/yellow]")
                self.console.print("  1. Your internet connection")
                self.console.print("  2. Your TWELVE_DATA_API_KEY in the .env file")
                self.console.print("  3. That the Twelve Data API service is available")
                return None

            # Use enhanced market data provider (now using Twelve Data API)
            market_data = self.enhanced_market_data_provider.get_comprehensive_data(symbol, trading_interval)

            if not market_data.get('quote'):
                self.console.print(f"[red]ERROR: Failed to get market data for {symbol}[/red]")
                self.console.print("[yellow]This could be due to:[/yellow]")
                self.console.print("  1. Invalid symbol name")
                self.console.print("  2. API rate limits")
                self.console.print("  3. Network connectivity issues")
                return None

            # Log current market data
            quote = market_data['quote']
            is_fx = market_data.get('is_fx', False)

            if is_fx:
                # For FX, we don't have change_percent, so log with 0
                log_market_data(self.logger, symbol, quote['price'], 0.0)
            else:
                # For stocks, use the change_percent
                change_percent = float(quote['change_percent'].rstrip('%'))
                log_market_data(self.logger, symbol, quote['price'], change_percent / 100)

            # Get current trade state for context
            current_state = self.signal_generator.get_current_trade_state(symbol)
            entry_price = self.signal_generator.entry_prices.get(symbol, 0.0)

            # Create trade state context for AI
            trade_state_context = ""
            if current_state != TradeState.NO_POSITION:
                current_price = market_data.get('quote', {}).get('price', 0.0)
                if entry_price > 0 and current_price > 0:
                    pnl_pct = ((current_price - entry_price) / entry_price) * 100
                    if current_state == TradeState.SHORT_POSITION:
                        pnl_pct = -pnl_pct
                    trade_state_context = f"Current Position: {current_state.value} entered at {entry_price:.5f}. Current P&L: {pnl_pct:+.2f}%"
                else:
                    trade_state_context = f"Current Position: {current_state.value}"
            else:
                trade_state_context = "Current Position: NO_POSITION - can enter new trades"

            # Perform AI analysis with trade state context
            ai_analysis = self.ai_analyzer.analyze_market_data(market_data, additional_context, trade_state_context)

            if not ai_analysis:
                self.console.print(f"[red]ERROR: AI analysis failed for {symbol}[/red]")
                self.console.print("[yellow]This could be due to:[/yellow]")
                self.console.print("  1. Invalid OpenAI API key")
                self.console.print("  2. OpenAI API rate limits")
                self.console.print("  3. Network connectivity issues")
                return None

            # Validate and enhance the analysis
            validated_analysis = self.ai_analyzer.validate_signal(ai_analysis, market_data)

            # Generate trading signal
            trading_signal = self.signal_generator.generate_signal(validated_analysis, market_data)

            if trading_signal:
                self.analysis_count += 1
                self.last_analysis_time = datetime.now()

                # Display signal in console
                self._display_signal(trading_signal, market_data)

            return trading_signal

        except Exception as e:
            log_error(self.logger, e, f"analyzing {symbol}")
            return None

    def _display_signal(self, signal: TradingSignal, market_data: Dict):
        """Display trading signal in a formatted table"""

        # Create signal table
        table = Table(title=f"Trading Signal for {signal.symbol}")
        table.add_column("Metric", style="cyan")
        table.add_column("Value", style="white")

        # Signal information
        signal_color = "green" if signal.signal.value == "BUY" else "red" if signal.signal.value == "SELL" else "orange" if signal.signal.value == "EXIT" else "yellow"
        table.add_row("Signal", f"[{signal_color}]{signal.signal.value}[/{signal_color}]")
        table.add_row("Confidence", f"{signal.confidence:.2%}")
        table.add_row("Risk Level", signal.risk_level.value)
        table.add_row("Time Horizon", signal.time_horizon)

        # Trade state information
        state_color = "green" if signal.current_trade_state == TradeState.LONG_POSITION else "red" if signal.current_trade_state == TradeState.SHORT_POSITION else "white"
        table.add_row("Trade State", f"[{state_color}]{signal.current_trade_state.value}[/{state_color}]")

        # Entry price and P&L if in position
        if signal.entry_price and signal.current_trade_state != TradeState.NO_POSITION:
            current_price = market_data.get('quote', {}).get('price', 0.0)
            if current_price > 0:
                pnl_pct = ((current_price - signal.entry_price) / signal.entry_price) * 100
                if signal.current_trade_state == TradeState.SHORT_POSITION:
                    pnl_pct = -pnl_pct
                pnl_color = "green" if pnl_pct >= 0 else "red"
                table.add_row("Entry Price", f"{signal.entry_price:.5f}")
                table.add_row("P&L", f"[{pnl_color}]{pnl_pct:+.2f}%[/{pnl_color}]")

        # Market data
        if market_data.get('quote'):
            quote = market_data['quote']
            is_fx = market_data.get('is_fx', False)

            if is_fx:
                # FX display format
                table.add_row("Current Rate", f"{quote['price']:.5f}")
                if 'bid' in quote and 'ask' in quote:
                    table.add_row("Bid/Ask", f"{quote['bid']:.5f} / {quote['ask']:.5f}")
                table.add_row("Last Refreshed", quote.get('last_refreshed', 'N/A'))
            else:
                # Stock display format
                table.add_row("Current Price", f"${quote['price']:.2f}")
                if 'change' in quote and 'change_percent' in quote:
                    table.add_row("Change", f"{quote['change']:+.2f} ({quote['change_percent']}%)")
                if 'volume' in quote:
                    table.add_row("Volume", f"{quote['volume']:,}")

        # Price targets
        if signal.price_target:
            table.add_row("Price Target", f"${signal.price_target:.2f}")
        if signal.stop_loss:
            table.add_row("Stop Loss", f"${signal.stop_loss:.2f}")

        # Validation flags
        if signal.validation_flags:
            table.add_row("Warnings", ", ".join(signal.validation_flags))

        self.console.print(table)

        # Display reasoning
        reasoning_panel = Panel(
            signal.reasoning,
            title="AI Analysis Reasoning",
            border_style="blue"
        )
        self.console.print(reasoning_panel)

        # Execution recommendation
        should_execute = self.signal_generator.should_execute_signal(signal)
        execution_color = "green" if should_execute else "red"
        execution_text = "EXECUTE" if should_execute else "DO NOT EXECUTE"

        execution_panel = Panel(
            f"[{execution_color}]{execution_text}[/{execution_color}]",
            title="Execution Recommendation",
            border_style=execution_color
        )
        self.console.print(execution_panel)
        self.console.print()  # Add spacing

    def get_user_symbol_input(self) -> str:
        """Get symbol input from user with validation"""
        self.console.print("\n[bold cyan]Symbol Selection[/bold cyan]")
        self.console.print("Enter a stock symbol (e.g., AAPL, MSFT) or FX pair (e.g., EUR/USD, GBP/USD)")

        # Show available symbols as examples
        if Config.ENABLE_STOCKS and Config.STOCK_SYMBOLS:
            stock_examples = [s.strip() for s in Config.STOCK_SYMBOLS[:5] if s.strip()]
            self.console.print(f"Stock examples: {', '.join(stock_examples)}")

        if Config.ENABLE_FX and Config.FX_SYMBOLS:
            fx_examples = [s.strip() for s in Config.FX_SYMBOLS[:5] if s.strip()]
            self.console.print(f"FX examples: {', '.join(fx_examples)}")

        while True:
            try:
                symbol = input("\nEnter symbol: ").strip().upper()

                if not symbol:
                    self.console.print("[red]Please enter a valid symbol[/red]")
                    continue

                # Basic validation
                if len(symbol) < 2:
                    self.console.print("[red]Symbol too short. Please enter a valid symbol[/red]")
                    continue

                # Check if it looks like FX pair
                if '/' in symbol:
                    parts = symbol.split('/')
                    if len(parts) != 2 or len(parts[0]) != 3 or len(parts[1]) != 3:
                        self.console.print("[red]FX pair format should be XXX/YYY (e.g., EUR/USD)[/red]")
                        continue

                self.console.print(f"[green]Selected symbol: {symbol}[/green]")
                return symbol

            except KeyboardInterrupt:
                self.console.print("\n[red]Operation cancelled by user[/red]")
                sys.exit(0)
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")
                continue

    def get_user_interval_input(self) -> str:
        """Get time interval input from user with validation"""
        self.console.print("\n[bold cyan]Time Interval Selection[/bold cyan]")

        # Create interval options table
        interval_table = Table(title="Available Time Intervals")
        interval_table.add_column("Option", style="cyan")
        interval_table.add_column("Interval", style="white")
        interval_table.add_column("Best For", style="yellow")

        intervals = {
            "1": ("1min", "Scalping, very short-term trades"),
            "2": ("5min", "Intraday trading, short-term analysis"),
            "3": ("15min", "Swing trading, medium-term analysis"),
            "4": ("30min", "Position trading, longer-term analysis"),
            "5": ("60min", "Daily analysis, trend following"),
            "6": ("1h", "Same as 60min (alternative format)")
        }

        for option, (interval, description) in intervals.items():
            interval_table.add_row(option, interval, description)

        self.console.print(interval_table)

        while True:
            try:
                choice = input("\nSelect interval (1-6) or enter custom (e.g., '1min', '5min'): ").strip()

                if not choice:
                    self.console.print("[red]Please make a selection[/red]")
                    continue

                # Check if it's a number choice
                if choice in intervals:
                    selected_interval = intervals[choice][0]
                    self.console.print(f"[green]Selected interval: {selected_interval}[/green]")
                    return selected_interval

                # Check if it's a custom interval
                choice_lower = choice.lower()
                valid_intervals = ['1min', '5min', '15min', '30min', '60min', '1h']

                if choice_lower in valid_intervals:
                    self.console.print(f"[green]Selected interval: {choice_lower}[/green]")
                    return choice_lower

                self.console.print(f"[red]Invalid interval. Please choose from: {', '.join(valid_intervals)}[/red]")

            except KeyboardInterrupt:
                self.console.print("\n[red]Operation cancelled by user[/red]")
                sys.exit(0)
            except Exception as e:
                self.console.print(f"[red]Error: {e}[/red]")
                continue

    def run_interactive_analysis(self):
        """Run interactive single analysis with user input"""
        self.console.print("\n[bold blue]AI Trading Bot - Interactive Mode[/bold blue]")
        self.console.print("Welcome! Let's analyze a symbol of your choice.")
        self.console.print("-" * 60)

        # Get user inputs
        symbol = self.get_user_symbol_input()
        interval = self.get_user_interval_input()

        # Optional context
        self.console.print("\n[bold cyan]Additional Context (Optional)[/bold cyan]")
        context = input("Enter any additional context for analysis (or press Enter to skip): ").strip()

        # Run analysis
        self.console.print(f"\n[bold blue]Starting Analysis[/bold blue]")
        self.console.print(f"Symbol: {symbol}")
        self.console.print(f"Interval: {interval}")
        self.console.print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        if context:
            self.console.print(f"Context: {context}")
        self.console.print("-" * 60)

        signal = self.analyze_symbol(symbol, interval, context)

        if signal:
            self.console.print(f"\n[green]Analysis completed successfully![/green]")

            # Ask if user wants to analyze another symbol
            self.console.print("\n[bold cyan]Continue Analysis?[/bold cyan]")
            while True:
                try:
                    continue_choice = input("Would you like to analyze another symbol? (y/n): ").strip().lower()
                    if continue_choice in ['y', 'yes']:
                        self.run_interactive_analysis()  # Recursive call
                        break
                    elif continue_choice in ['n', 'no']:
                        self.console.print("[green]Thank you for using the AI Trading Bot![/green]")
                        break
                    else:
                        self.console.print("[red]Please enter 'y' for yes or 'n' for no[/red]")
                except KeyboardInterrupt:
                    self.console.print("\n[green]Goodbye![/green]")
                    break
        else:
            self.console.print(f"\n[red]Analysis failed. Please try again with a different symbol.[/red]")

        return signal

    def run_continuous_interactive_analysis(self):
        """Run continuous analysis with user-selected symbol and interval"""
        self.console.print("\n[bold blue]AI Trading Bot - Continuous Analysis Mode[/bold blue]")
        self.console.print("This mode will continuously analyze your chosen symbol and track trade states.")
        self.console.print("The bot will give BUY signals, then HOLD until it recommends EXIT.")
        self.console.print("-" * 60)

        # Get user inputs
        symbol = self.get_user_symbol_input()
        interval = self.get_user_interval_input()

        # Optional context
        self.console.print("\n[bold cyan]Additional Context (Optional)[/bold cyan]")
        context = input("Enter any additional context for analysis (or press Enter to skip): ").strip()

        # Convert interval to minutes for scheduling
        interval_minutes = self._convert_interval_to_minutes(interval)

        self.console.print(f"\n[bold blue]Starting Continuous Analysis[/bold blue]")
        self.console.print(f"Symbol: {symbol}")
        self.console.print(f"Interval: {interval} ({interval_minutes} minutes)")
        self.console.print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        if context:
            self.console.print(f"Context: {context}")
        self.console.print("-" * 60)
        self.console.print("[red]Press Ctrl+C to stop the analysis[/red]")
        self.console.print("-" * 60)

        self.is_running = True
        next_analysis_time = datetime.now()

        try:
            while self.is_running:
                current_time = datetime.now()

                # Check if it's time for the next analysis
                if current_time >= next_analysis_time:
                    self.console.print(f"\n[cyan]Analysis at {current_time.strftime('%H:%M:%S')}[/cyan]")

                    # Run analysis
                    signal = self.analyze_symbol(symbol, interval, context)

                    if signal:
                        # Show current trade state
                        trade_state = self.signal_generator.get_current_trade_state(symbol)
                        self.console.print(f"Current State: {trade_state.value}")

                        # Show next analysis time
                        next_analysis_time = current_time + timedelta(minutes=interval_minutes)
                        self.console.print(f"Next analysis: {next_analysis_time.strftime('%H:%M:%S')}")
                    else:
                        self.console.print("[red]Analysis failed, retrying in 1 minute[/red]")
                        next_analysis_time = current_time + timedelta(minutes=1)

                    self.console.print("-" * 60)

                # Sleep for 30 seconds before checking again
                time.sleep(30)

        except KeyboardInterrupt:
            self.console.print("\n[yellow]Continuous analysis stopped by user[/yellow]")
        finally:
            self.stop()

    def _convert_interval_to_minutes(self, interval: str) -> int:
        """Convert interval string to minutes"""
        interval_lower = interval.lower()
        if interval_lower == '1min':
            return 1
        elif interval_lower == '5min':
            return 5
        elif interval_lower == '15min':
            return 15
        elif interval_lower == '30min':
            return 30
        elif interval_lower in ['60min', '1h']:
            return 60
        else:
            # Default to 5 minutes if unknown
            return 5

    def run_single_analysis(self, symbol: str = None, interval: str = None, context: str = ""):
        """
        Run a single analysis cycle

        Args:
            symbol: Symbol to analyze (uses default if None)
            interval: Time interval for analysis (uses config default if None)
            context: Additional context for analysis
        """
        symbol = symbol or Config.DEFAULT_SYMBOL
        interval = interval or Config.get_trading_interval()

        self.console.print(f"\n[bold blue]Starting Trading Bot Analysis[/bold blue]")
        self.console.print(f"Symbol: {symbol}")
        self.console.print(f"Interval: {interval}")
        self.console.print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.console.print("-" * 60)

        signal = self.analyze_symbol(symbol, interval, context)

        if signal:
            self.console.print(f"[green]Analysis completed successfully[/green]")
        else:
            self.console.print(f"[red]Analysis failed[/red]")

        return signal

    def run_continuous(self, symbols: List[str] = None, interval_minutes: int = 15):
        """
        Run bot continuously with scheduled analysis

        Args:
            symbols: List of symbols to analyze
            interval_minutes: Minutes between analysis cycles
        """
        symbols = symbols or [Config.DEFAULT_SYMBOL]

        self.console.print(f"\n[bold green]Starting Continuous Trading Bot[/bold green]")
        self.console.print(f"Symbols: {', '.join(symbols)}")
        self.console.print(f"Interval: {interval_minutes} minutes")
        self.console.print(f"Model: {Config.OPENAI_MODEL}")
        self.console.print("-" * 60)

        # Schedule analysis for each symbol
        for symbol in symbols:
            schedule.every(interval_minutes).minutes.do(self.analyze_symbol, symbol)

        self.is_running = True

        try:
            while self.is_running:
                schedule.run_pending()
                time.sleep(30)  # Check every 30 seconds

        except KeyboardInterrupt:
            self.logger.info("Bot stopped by user")
        finally:
            self.stop()

    def stop(self):
        """Stop the trading bot"""
        self.is_running = False

        # Export signals before stopping (only if there are actionable signals)
        if self.signal_generator.signal_history:
            filename = self.signal_generator.export_signals()
            self.console.print(f"Final signal summary exported to: {filename}")
        else:
            self.console.print("No actionable signals to export (HOLD signals are not saved to JSON)")

        # Cleanup enhanced market data provider
        self.enhanced_market_data_provider.cleanup()

        # Display summary
        self._display_summary()

        self.logger.info("Trading bot stopped")

    def display_api_status(self):
        """Display Twelve Data API connection status and statistics"""
        self.console.print("\n[bold blue]Twelve Data API Status[/bold blue]")

        # Get API statistics
        stats = self.enhanced_market_data_provider.get_api_statistics()

        # Create status table
        table = Table(title="Twelve Data API Connection")
        table.add_column("Metric", style="cyan")
        table.add_column("Status", style="green")

        # Connection status
        connection_status = "Connected" if stats['api_connected'] else "Disconnected"
        table.add_row("API Connection", connection_status)

        table.add_row("Data Source", stats['data_source'])
        table.add_row("Cached Symbols", str(len(stats['cached_symbols'])))
        table.add_row("Cache Size", str(stats['cache_size']))

        self.console.print(table)

        # Show cached symbols
        if stats['cached_symbols']:
            self.console.print(f"\nCached Symbols: {', '.join(stats['cached_symbols'])}")

            # Show latest quotes
            quote_table = Table(title="Latest Cached Quotes")
            quote_table.add_column("Symbol", style="cyan")
            quote_table.add_column("Price", style="green")
            quote_table.add_column("Source", style="yellow")

            for symbol in stats['cached_symbols'][:5]:  # Show first 5 symbols
                quote = self.enhanced_market_data_provider.get_live_quote(symbol)
                if quote:
                    quote_table.add_row(
                        symbol,
                        f"{quote['price']:.5f}",
                        quote.get('source', 'Unknown')
                    )

            self.console.print(quote_table)

    def _display_summary(self):
        """Display bot performance summary"""
        summary = self.signal_generator.get_signal_summary()

        summary_table = Table(title="Bot Performance Summary")
        summary_table.add_column("Metric", style="cyan")
        summary_table.add_column("Value", style="white")

        summary_table.add_row("Total Analyses", str(self.analysis_count))
        summary_table.add_row("Total Signals", str(summary.get('total_signals', 0)))
        summary_table.add_row("Buy Signals", str(summary.get('buy_signals', 0)))
        summary_table.add_row("Sell Signals", str(summary.get('sell_signals', 0)))
        summary_table.add_row("Hold Signals", str(summary.get('hold_signals', 0)))
        summary_table.add_row("Exit Signals", str(summary.get('exit_signals', 0)))

        if summary.get('average_confidence'):
            summary_table.add_row("Avg Confidence", f"{summary['average_confidence']:.2%}")

        if self.last_analysis_time:
            summary_table.add_row("Last Analysis", self.last_analysis_time.strftime('%Y-%m-%d %H:%M:%S'))

        self.console.print(summary_table)

def main():
    """Main entry point"""
    import argparse

    parser = argparse.ArgumentParser(description="AI-Powered Trading Bot")
    parser.add_argument("--symbol", "-s", default=Config.DEFAULT_SYMBOL, help="Single symbol to analyze")
    parser.add_argument("--continuous", "-c", action="store_true", help="Run continuously")
    parser.add_argument("--interval", "-i", type=int, default=15, help="Analysis interval in minutes")
    parser.add_argument("--context", default="", help="Additional context for analysis")

    # Multiple symbol options
    parser.add_argument("--all-symbols", "-a", action="store_true", help="Analyze all configured symbols")
    parser.add_argument("--stocks-only", action="store_true", help="Analyze only stock symbols")
    parser.add_argument("--fx-only", action="store_true", help="Analyze only FX symbols")
    parser.add_argument("--scalping", action="store_true", help="Enable scalping mode (1min intervals)")

    # Interactive mode
    parser.add_argument("--interactive", action="store_true", help="Run in interactive mode (asks for user input)")

    args = parser.parse_args()

    # Validate configuration
    if not Config.validate_config():
        print("❌ Configuration validation failed. Please check your .env file.")
        return

    # Create bot instance
    bot = TradingBot()

    # Check if interactive mode is requested
    if args.interactive:
        # Run interactive mode - ignores other arguments
        bot.run_interactive_analysis()
        return

    Config.print_config()

    # Determine symbols to analyze
    symbols_to_analyze = []

    if args.all_symbols:
        symbols_to_analyze = Config.get_all_symbols()
    elif args.stocks_only:
        if Config.ENABLE_STOCKS:
            symbols_to_analyze = [s.strip() for s in Config.STOCK_SYMBOLS if s.strip()]
        else:
            print("Stock trading is disabled in configuration.")
            return
    elif args.fx_only:
        if Config.ENABLE_FX:
            symbols_to_analyze = [s.strip() for s in Config.FX_SYMBOLS if s.strip()]
        else:
            print("FX trading is disabled in configuration.")
            return
    else:
        symbols_to_analyze = [args.symbol]

    if not symbols_to_analyze:
        print("❌ No symbols to analyze.")
        return

    # Override interval for scalping mode
    if args.scalping:
        interval_minutes = 1
        print("🏃 Scalping mode enabled - using 1-minute intervals")
    else:
        interval_minutes = args.interval

    if args.continuous:
        print(f"🔄 Running continuous analysis for {len(symbols_to_analyze)} symbols")
        bot.run_continuous(symbols_to_analyze, interval_minutes)
    else:
        if len(symbols_to_analyze) == 1:
            bot.run_single_analysis(symbols_to_analyze[0], None, args.context)
        else:
            # Run single analysis for multiple symbols
            print(f"📊 Running single analysis for {len(symbols_to_analyze)} symbols")
            for symbol in symbols_to_analyze:
                print(f"\n{'='*60}")
                bot.run_single_analysis(symbol, None, args.context)
                time.sleep(2)  # Brief pause between analyses

if __name__ == "__main__":
    main()
