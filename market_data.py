"""
Market Data Module - Twelve Data API Integration
"""
import requests
import time
import pandas as pd
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from config import Config
from logger_config import setup_logger, log_api_call, log_error

class MarketDataProvider:
    """Twelve Data API client for fetching market data"""

    def __init__(self):
        self.logger = setup_logger("MarketData")
        self.base_url = Config.TWELVE_DATA_BASE_URL
        self.api_key = Config.TWELVE_DATA_API_KEY
        self.session = requests.Session()

    def _make_request(self, endpoint: str, params: Dict) -> Optional[Dict]:
        """
        Make API request with error handling and rate limiting

        Args:
            endpoint: API endpoint (e.g., 'time_series', 'quote')
            params: API parameters

        Returns:
            API response data or None if failed
        """
        params['apikey'] = self.api_key
        url = f"{self.base_url}/{endpoint}"

        try:
            response = self.session.get(url, params=params, timeout=30)
            response.raise_for_status()

            data = response.json()

            # Check for API error messages
            if 'status' in data and data['status'] == 'error':
                self.logger.error(f"Twelve Data API Error: {data.get('message', 'Unknown error')}")
                return None

            log_api_call(self.logger, "Twelve Data", endpoint, "success")
            time.sleep(Config.API_CALL_DELAY)  # Rate limiting
            return data

        except requests.exceptions.RequestException as e:
            log_error(self.logger, e, "Twelve Data API request")
            return None

    def get_intraday_data(self, symbol: str, interval: str = '5min') -> Optional[pd.DataFrame]:
        """
        Get intraday data for stocks and FX

        Args:
            symbol: Symbol (e.g., 'AAPL', 'EUR/USD')
            interval: Time interval ('1min', '5min', '15min', '30min', '1h')

        Returns:
            DataFrame with OHLCV data or None if failed
        """
        params = {
            'symbol': symbol,
            'interval': interval,
            'outputsize': 100
        }

        data = self._make_request('time_series', params)
        if not data:
            return None

        try:
            if 'values' not in data:
                self.logger.error(f"No 'values' key found in response for {symbol}")
                return None

            values = data['values']
            if not values:
                self.logger.warning(f"No data values returned for {symbol}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(values)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            df = df.sort_index()

            # Convert to numeric and rename columns
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col])

            self.logger.info(f"📊 Retrieved {len(df)} intraday data points for {symbol}")
            return df

        except Exception as e:
            log_error(self.logger, e, "parsing intraday data")
            return None

    def get_daily_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """
        Get daily data for stocks and FX

        Args:
            symbol: Symbol (e.g., 'AAPL', 'EUR/USD')

        Returns:
            DataFrame with daily OHLCV data or None if failed
        """
        params = {
            'symbol': symbol,
            'interval': '1day',
            'outputsize': 100
        }

        data = self._make_request('time_series', params)
        if not data:
            return None

        try:
            if 'values' not in data:
                self.logger.error(f"No 'values' key found in response for {symbol}")
                return None

            values = data['values']
            if not values:
                self.logger.warning(f"No data values returned for {symbol}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(values)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            df = df.sort_index()

            # Convert to numeric
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col])

            self.logger.info(f"📊 Retrieved {len(df)} daily data points for {symbol}")
            return df

        except Exception as e:
            log_error(self.logger, e, "parsing daily data")
            return None

    def get_technical_indicator(self, symbol: str, indicator: str, interval: str = '1day', **kwargs) -> Optional[pd.DataFrame]:
        """
        Get technical indicator data

        Args:
            symbol: Symbol (e.g., 'AAPL', 'EUR/USD')
            indicator: Technical indicator name (RSI, MACD, SMA, etc.)
            interval: Time interval
            **kwargs: Additional parameters for the indicator

        Returns:
            DataFrame with indicator data or None if failed
        """
        params = {
            'symbol': symbol,
            'interval': interval,
            'outputsize': 50
        }

        # Add indicator-specific parameters
        if indicator.upper() == 'SMA':
            params['time_period'] = kwargs.get('time_period', 20)
        elif indicator.upper() == 'EMA':
            params['time_period'] = kwargs.get('time_period', 20)
        elif indicator.upper() == 'RSI':
            params['time_period'] = kwargs.get('time_period', 14)
        elif indicator.upper() == 'BBANDS':
            params['time_period'] = kwargs.get('time_period', 20)
            params['sd'] = kwargs.get('sd', 2)
        elif indicator.upper() == 'STOCH':
            params['k_period'] = kwargs.get('k_period', 14)
            params['d_period'] = kwargs.get('d_period', 3)

        data = self._make_request(indicator.lower(), params)
        if not data:
            return None

        try:
            if 'values' not in data:
                self.logger.error(f"No 'values' key found in {indicator} response for {symbol}")
                return None

            values = data['values']
            if not values:
                self.logger.warning(f"No {indicator} data values returned for {symbol}")
                return None

            # Convert to DataFrame
            df = pd.DataFrame(values)
            df['datetime'] = pd.to_datetime(df['datetime'])
            df.set_index('datetime', inplace=True)
            df = df.sort_index()

            # Convert to numeric
            for col in df.columns:
                if col != 'datetime':
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            self.logger.info(f"📈 Retrieved {indicator} data for {symbol}")
            return df

        except Exception as e:
            log_error(self.logger, e, f"parsing {indicator} data")
            return None

    def get_quote(self, symbol: str) -> Optional[Dict]:
        """
        Get real-time quote for a symbol

        Args:
            symbol: Symbol (e.g., 'AAPL', 'EUR/USD')

        Returns:
            Dictionary with quote data or None if failed
        """
        params = {
            'symbol': symbol
        }

        data = self._make_request('quote', params)
        if not data:
            return None

        try:
            quote = {
                'symbol': data.get('symbol', symbol),
                'price': float(data.get('close', 0)),
                'change': float(data.get('change', 0)),
                'change_percent': data.get('percent_change', '0').rstrip('%'),
                'volume': int(data.get('volume', 0)),
                'last_refreshed': data.get('datetime', ''),
                'source': 'Twelve Data'
            }

            # Add bid/ask for FX pairs if available
            if Config.is_fx_symbol(symbol):
                quote['bid'] = quote['price']  # For FX, close price is typically mid-price
                quote['ask'] = quote['price']
                quote['spread'] = 0.0

            self.logger.info(f"💰 Quote for {symbol}: ${quote['price']:.4f}")
            return quote

        except Exception as e:
            log_error(self.logger, e, "parsing quote data")
            return None

    def get_comprehensive_data(self, symbol: str, interval: str = '5min') -> Dict:
        """
        Get comprehensive market data including price, volume, and technical indicators

        Args:
            symbol: Symbol (e.g., 'AAPL', 'EUR/USD')
            interval: Time interval for intraday data

        Returns:
            Dictionary containing all market data
        """
        self.logger.info(f"🔍 Fetching comprehensive data for {symbol}")

        result = {
            'symbol': symbol,
            'timestamp': datetime.now(),
            'quote': None,
            'intraday': None,
            'daily': None,
            'technical_indicators': {},
            'is_fx': Config.is_fx_symbol(symbol),
            'data_source': 'Twelve Data'
        }

        # Get quote (works for both stocks and FX)
        result['quote'] = self.get_quote(symbol)

        # Get intraday data
        result['intraday'] = self.get_intraday_data(symbol, interval)

        # Get daily data
        result['daily'] = self.get_daily_data(symbol)

        # Get technical indicators
        for indicator in Config.TECHNICAL_INDICATORS:
            indicator_data = self.get_technical_indicator(symbol, indicator, interval='1day')
            if indicator_data is not None:
                result['technical_indicators'][indicator] = indicator_data

        return result
