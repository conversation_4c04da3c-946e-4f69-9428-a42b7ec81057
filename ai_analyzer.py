"""
AI Analysis Module - OpenAI API Integration for Market Analysis
"""
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import pandas as pd
from openai import OpenAI
from config import Config
from logger_config import setup_logger, log_api_call, log_error

class AIMarketAnalyzer:
    """OpenAI-powered market analysis for trading signals"""

    def __init__(self):
        self.logger = setup_logger("AIAnalyzer")
        self.client = OpenAI(api_key=Config.OPENAI_API_KEY)
        self.model = Config.OPENAI_MODEL

    def _format_market_data(self, market_data: Dict) -> str:
        """
        Format market data for AI analysis

        Args:
            market_data: Comprehensive market data dictionary

        Returns:
            Formatted string for AI prompt
        """
        formatted_data = []

        # Quote information
        if market_data.get('quote'):
            quote = market_data['quote']
            is_fx = market_data.get('is_fx', False)

            if is_fx:
                # FX quote format
                formatted_data.append(f"Current FX Quote for {quote['symbol']}:")
                formatted_data.append(f"  Exchange Rate: {quote['price']:.5f}")
                if 'bid' in quote and 'ask' in quote:
                    formatted_data.append(f"  Bid: {quote['bid']:.5f}")
                    formatted_data.append(f"  Ask: {quote['ask']:.5f}")
                    spread = quote['ask'] - quote['bid']
                    formatted_data.append(f"  Spread: {spread:.5f}")
                formatted_data.append(f"  Last Refreshed: {quote.get('last_refreshed', 'N/A')}")
                formatted_data.append(f"  Timezone: {quote.get('timezone', 'N/A')}")
            else:
                # Stock quote format
                formatted_data.append(f"Current Quote for {quote['symbol']}:")
                formatted_data.append(f"  Price: ${quote['price']:.2f}")
                if 'change' in quote and 'change_percent' in quote:
                    formatted_data.append(f"  Change: {quote['change']:+.2f} ({quote['change_percent']}%)")
                if 'volume' in quote:
                    formatted_data.append(f"  Volume: {quote['volume']:,}")
                if 'latest_trading_day' in quote:
                    formatted_data.append(f"  Latest Trading Day: {quote['latest_trading_day']}")
            formatted_data.append("")

        # Recent price action from intraday data
        if market_data.get('intraday') is not None and not market_data['intraday'].empty:
            intraday = market_data['intraday'].tail(10)  # Last 10 periods
            is_fx = market_data.get('is_fx', False)
            formatted_data.append("Recent Intraday Price Action (Last 10 periods):")

            for timestamp, row in intraday.iterrows():
                if is_fx:
                    # FX data doesn't have volume
                    formatted_data.append(f"  {timestamp}: O:{row['open']:.5f} H:{row['high']:.5f} L:{row['low']:.5f} C:{row['close']:.5f}")
                else:
                    # Stock data has volume
                    if 'volume' in row:
                        formatted_data.append(f"  {timestamp}: O:{row['open']:.2f} H:{row['high']:.2f} L:{row['low']:.2f} C:{row['close']:.2f} V:{int(row['volume']):,}")
                    else:
                        formatted_data.append(f"  {timestamp}: O:{row['open']:.2f} H:{row['high']:.2f} L:{row['low']:.2f} C:{row['close']:.2f}")
            formatted_data.append("")

        # Technical indicators
        if market_data.get('technical_indicators'):
            formatted_data.append("Technical Indicators:")
            for indicator, data in market_data['technical_indicators'].items():
                if data is not None and not data.empty:
                    latest = data.iloc[-1]
                    formatted_data.append(f"  {indicator}:")
                    for col, value in latest.items():
                        formatted_data.append(f"    {col}: {value:.4f}")
            formatted_data.append("")

        return "\n".join(formatted_data)

    def _create_analysis_prompt(self, market_data: Dict, additional_context: str = "", trade_state_context: str = "") -> str:
        """
        Create a comprehensive prompt for AI analysis

        Args:
            market_data: Market data dictionary
            additional_context: Additional context or instructions
            trade_state_context: Current trade state information

        Returns:
            Formatted prompt string
        """
        formatted_data = self._format_market_data(market_data)
        is_fx = market_data.get('is_fx', False)
        asset_type = "FX pair" if is_fx else "stock"

        # Customize analysis factors based on asset type
        if is_fx:
            analysis_factors = """
1. Technical indicators and their signals
2. Price action and momentum patterns
3. Currency pair dynamics and correlations
4. Economic fundamentals and central bank policies
5. Risk-reward ratio and volatility
6. Global market sentiment and risk appetite
7. Support and resistance levels
8. Trend analysis and breakout patterns"""
        else:
            analysis_factors = """
1. Technical indicators and their signals
2. Price action and momentum
3. Volume analysis and liquidity
4. Market trends and patterns
5. Risk-reward ratio
6. Current market conditions
7. Sector performance and relative strength
8. Earnings and fundamental factors"""

        # Add trade state context if provided
        trade_state_instruction = ""
        if trade_state_context:
            trade_state_instruction = f"""

IMPORTANT - CURRENT TRADE STATE:
{trade_state_context}

Based on the current trade state:
- If NO POSITION: You can recommend BUY, SELL, or HOLD
- If LONG POSITION: Only recommend HOLD (to maintain position) or SELL/EXIT (to close position)
- If SHORT POSITION: Only recommend HOLD (to maintain position) or BUY/EXIT (to close position)
- Consider the current P&L when deciding whether to hold or exit the position"""

        prompt = f"""You are an expert financial analyst and trader with deep knowledge of technical analysis, market psychology, and quantitative trading strategies.

Analyze the following {asset_type} market data and provide a trading recommendation:

{formatted_data}

{additional_context}{trade_state_instruction}

Please provide your analysis in the following JSON format:
{{
    "signal": "BUY" | "SELL" | "HOLD" | "EXIT",
    "confidence": 0.0-1.0,
    "reasoning": "Detailed explanation of your analysis and reasoning",
    "key_factors": ["factor1", "factor2", "factor3"],
    "risk_assessment": "LOW" | "MEDIUM" | "HIGH",
    "price_target": number or null,
    "stop_loss": number or null,
    "time_horizon": "SHORT" | "MEDIUM" | "LONG"
}}

Consider the following in your analysis:{analysis_factors}

Be objective and conservative in your analysis. Only recommend BUY or SELL if you have strong conviction based on the data.
{"For FX pairs, consider both currencies' strength and economic factors." if is_fx else "For stocks, pay attention to volume confirmation and sector trends."}

If you are currently in a position, carefully evaluate whether to continue holding or exit based on the current market conditions and your analysis."""

        return prompt

    def analyze_market_data(self, market_data: Dict, additional_context: str = "", trade_state_context: str = "") -> Optional[Dict]:
        """
        Analyze market data using OpenAI and return trading signal

        Args:
            market_data: Comprehensive market data
            additional_context: Additional context for analysis
            trade_state_context: Current trade state information

        Returns:
            Analysis result dictionary or None if failed
        """
        try:
            prompt = self._create_analysis_prompt(market_data, additional_context, trade_state_context)

            self.logger.info(f"[AI] Analyzing market data for {market_data.get('symbol', 'Unknown')}")

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional financial analyst. Provide objective, data-driven trading analysis."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=Config.OPENAI_MAX_TOKENS,
                temperature=Config.OPENAI_TEMPERATURE
            )

            log_api_call(self.logger, "OpenAI", self.model, "success")

            # Parse the response
            content = response.choices[0].message.content.strip()

            # Try to extract JSON from the response
            try:
                # Find JSON in the response
                start_idx = content.find('{')
                end_idx = content.rfind('}') + 1

                if start_idx != -1 and end_idx != 0:
                    json_str = content[start_idx:end_idx]
                    analysis = json.loads(json_str)

                    # Validate required fields
                    required_fields = ['signal', 'confidence', 'reasoning']
                    if all(field in analysis for field in required_fields):
                        analysis['timestamp'] = datetime.now()
                        analysis['model_used'] = self.model
                        analysis['raw_response'] = content

                        self.logger.info(f"[AI] Analysis complete: {analysis['signal']} (confidence: {analysis['confidence']:.2%})")
                        return analysis
                    else:
                        self.logger.error("Missing required fields in AI response")
                        return None

                else:
                    self.logger.error("No valid JSON found in AI response")
                    return None

            except json.JSONDecodeError as e:
                self.logger.error(f"Failed to parse AI response as JSON: {e}")
                self.logger.debug(f"Raw response: {content}")
                return None

        except Exception as e:
            log_error(self.logger, e, "OpenAI API analysis")
            return None

    def get_market_sentiment(self, symbol: str, news_context: str = "") -> Optional[Dict]:
        """
        Analyze market sentiment for a given symbol

        Args:
            symbol: Stock symbol
            news_context: Recent news or market context

        Returns:
            Sentiment analysis result
        """
        try:
            prompt = f"""Analyze the current market sentiment for {symbol}.

{news_context}

Consider:
1. Overall market conditions
2. Sector performance
3. Recent news and events
4. Investor sentiment indicators

Provide your sentiment analysis in JSON format:
{{
    "sentiment": "BULLISH" | "BEARISH" | "NEUTRAL",
    "confidence": 0.0-1.0,
    "key_drivers": ["driver1", "driver2"],
    "outlook": "SHORT_TERM" | "MEDIUM_TERM" | "LONG_TERM"
}}"""

            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a market sentiment analyst."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=500,
                temperature=0.3
            )

            content = response.choices[0].message.content.strip()

            # Parse JSON response
            start_idx = content.find('{')
            end_idx = content.rfind('}') + 1

            if start_idx != -1 and end_idx != 0:
                json_str = content[start_idx:end_idx]
                sentiment = json.loads(json_str)
                sentiment['timestamp'] = datetime.now()
                return sentiment

            return None

        except Exception as e:
            log_error(self.logger, e, "sentiment analysis")
            return None

    def validate_signal(self, analysis: Dict, market_data: Dict) -> Dict:
        """
        Validate and enhance the trading signal with additional checks

        Args:
            analysis: AI analysis result
            market_data: Market data used for analysis

        Returns:
            Enhanced analysis with validation
        """
        enhanced_analysis = analysis.copy()

        # Add validation flags
        validation_flags = []

        # Check confidence threshold
        if analysis['confidence'] < 0.6:
            validation_flags.append("LOW_CONFIDENCE")

        # Check for sufficient volume (only for stocks)
        is_fx = market_data.get('is_fx', False)
        if not is_fx and market_data.get('quote') and 'volume' in market_data['quote']:
            if market_data['quote']['volume'] < 100000:
                validation_flags.append("LOW_VOLUME")

        # Check for extreme price movements (only for stocks with change data)
        if not is_fx and market_data.get('quote') and 'change_percent' in market_data['quote']:
            change_percent = float(market_data['quote']['change_percent'].rstrip('%'))
            if abs(change_percent) > 10:
                validation_flags.append("EXTREME_MOVEMENT")

        enhanced_analysis['validation_flags'] = validation_flags
        enhanced_analysis['validated_at'] = datetime.now()

        # Adjust confidence based on validation flags
        if validation_flags:
            penalty = len(validation_flags) * 0.1
            enhanced_analysis['adjusted_confidence'] = max(0.0, analysis['confidence'] - penalty)
        else:
            enhanced_analysis['adjusted_confidence'] = analysis['confidence']

        return enhanced_analysis
