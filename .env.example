# Twelve Data API Configuration
TWELVE_DATA_API_KEY=86652d916d9a4edc97166fe563183ed9

# OpenAI API Configuration
OPENAI_API_KEY=***********************************************************************************************

# Trading Bot Configuration
DEFAULT_SYMBOL=AAPL
ANALYSIS_INTERVAL=5min
LOG_LEVEL=INFO

# Multiple Symbol Configuration
# Stock symbols (comma-separated, no spaces)
STOCK_SYMBOLS=AAPL,MSFT,GOOGL,TSLA,NVDA,SPY,QQQ,AMZN,META,NFLX

# FX symbols (comma-separated, no spaces)
FX_SYMBOLS=EUR/USD,GBP/USD,USD/JPY,AUD/USD,USD/CAD,USD/CHF,NZD/USD,EUR/GBP

# Trading Strategy Configuration
ENABLE_STOCKS=true
ENABLE_FX=true

# Scalping and Intraday Configuration
SCALPING_MODE=True
SCALPING_INTERVAL=1min
INTRADAY_INTERVAL=5min

# Optional: Premium features
TWELVE_DATA_PREMIUM=false

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Get your Twelve Data API key from: https://twelvedata.com/pricing
# 2. Get your OpenAI API key from: https://platform.openai.com/api-keys
# 3. Copy this file to .env and replace the placeholder values
# 4. Run the bot with: python interactive_bot.py
#
# TWELVE DATA API FEATURES:
# - Real-time and historical data for stocks and FX
# - Technical indicators (RSI, MACD, SMA, EMA, etc.)
# - Free tier available with 800 API calls per day
# - Premium plans for higher limits and additional features
