"""
Configuration management for the Trading Bot
"""
import os
from dotenv import load_dotenv
from typing import Optional, List

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the trading bot"""

    # API Keys
    TWELVE_DATA_API_KEY: str = os.getenv('TWELVE_DATA_API_KEY', '')
    OPENAI_API_KEY: str = os.getenv('OPENAI_API_KEY', '')

    # Trading Configuration
    DEFAULT_SYMBOL: str = os.getenv('DEFAULT_SYMBOL', 'AAPL')
    ANALYSIS_INTERVAL: str = os.getenv('ANALYSIS_INTERVAL', '5min')

    # Multiple Symbol Configuration
    STOCK_SYMBOLS: List[str] = os.getenv('STOCK_SYMBOLS', 'AAPL,MSFT,GOOGL,TSLA,NVDA').split(',')
    FX_SYMBOLS: List[str] = os.getenv('FX_SYMBOLS', 'EUR/USD,GBP/USD,USD/JPY,AUD/USD,USD/CAD').split(',')

    # Trading Strategy Configuration
    ENABLE_STOCKS: bool = os.getenv('ENABLE_STOCKS', 'true').lower() == 'true'
    ENABLE_FX: bool = os.getenv('ENABLE_FX', 'true').lower() == 'true'

    # Scalping Configuration
    SCALPING_MODE: bool = os.getenv('SCALPING_MODE', 'false').lower() == 'true'
    SCALPING_INTERVAL: str = os.getenv('SCALPING_INTERVAL', '1min')
    INTRADAY_INTERVAL: str = os.getenv('INTRADAY_INTERVAL', '5min')

    # Logging Configuration
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')

    # Twelve Data Configuration
    TWELVE_DATA_BASE_URL: str = 'https://api.twelvedata.com'
    TWELVE_DATA_PREMIUM: bool = os.getenv('TWELVE_DATA_PREMIUM', 'false').lower() == 'true'

    # OpenAI Configuration
    OPENAI_MODEL: str = 'gpt-4o'  # Latest model as of 2025
    OPENAI_MAX_TOKENS: int = 1000
    OPENAI_TEMPERATURE: float = 0.3  # Lower temperature for more consistent analysis

    # Rate Limiting
    API_CALL_DELAY: float = 1.0  # Seconds between API calls
    MAX_RETRIES: int = 3

    # Technical Indicators to fetch (optimized for 8 API request limit)
    TECHNICAL_INDICATORS = [
        'RSI',
        'MACD',
        'EMA',
        'BBANDS'
    ]



    @classmethod
    def validate_config(cls) -> bool:
        """Validate that required configuration is present"""
        # Twelve Data API key is required for market data
        if not cls.TWELVE_DATA_API_KEY:
            print("❌ TWELVE_DATA_API_KEY is required for market data")
            return False

        if not cls.OPENAI_API_KEY:
            print("❌ OPENAI_API_KEY is required")
            return False

        if not cls.ENABLE_STOCKS and not cls.ENABLE_FX:
            print("❌ At least one of ENABLE_STOCKS or ENABLE_FX must be true")
            return False

        return True

    @classmethod
    def get_all_symbols(cls) -> List[str]:
        """Get all enabled symbols (stocks + FX)"""
        symbols = []
        if cls.ENABLE_STOCKS:
            symbols.extend([s.strip() for s in cls.STOCK_SYMBOLS if s.strip()])
        if cls.ENABLE_FX:
            symbols.extend([s.strip() for s in cls.FX_SYMBOLS if s.strip()])
        return symbols

    @classmethod
    def get_trading_interval(cls) -> str:
        """Get appropriate trading interval based on mode"""
        return cls.SCALPING_INTERVAL if cls.SCALPING_MODE else cls.INTRADAY_INTERVAL

    @classmethod
    def is_fx_symbol(cls, symbol: str) -> bool:
        """Check if symbol is a FX pair"""
        # Check if symbol contains slash (e.g., EUR/USD)
        if '/' in symbol:
            return True

        # Check if symbol is in FX_SYMBOLS list (exact match)
        if symbol in [s.strip() for s in cls.FX_SYMBOLS]:
            return True

        # Check if symbol matches FX pairs without slash (e.g., EURUSD matches EUR/USD)
        for fx_symbol in cls.FX_SYMBOLS:
            fx_clean = fx_symbol.strip().replace('/', '')
            if symbol.upper() == fx_clean.upper():
                return True

        # Common FX pairs without slash format
        common_fx_pairs = [
            'EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF',
            'NZDUSD', 'EURGBP', 'EURJPY', 'GBPJPY', 'AUDJPY', 'CADJPY',
            'CHFJPY', 'EURCHF', 'EURAUD', 'EURNZD', 'GBPAUD', 'GBPCAD',
            'GBPCHF', 'GBPNZD', 'AUDCAD', 'AUDCHF', 'AUDNZD', 'CADCHF',
            'NZDCAD', 'NZDCHF', 'NZDJPY'
        ]

        return symbol.upper() in common_fx_pairs

    @classmethod
    def print_config(cls) -> None:
        """Print current configuration (without sensitive data)"""
        print("🔧 Trading Bot Configuration:")
        print(f"   Default Symbol: {cls.DEFAULT_SYMBOL}")
        print(f"   Analysis Interval: {cls.ANALYSIS_INTERVAL}")
        print(f"   OpenAI Model: {cls.OPENAI_MODEL}")
        print(f"   Log Level: {cls.LOG_LEVEL}")
        print(f"   Twelve Data Premium: {cls.TWELVE_DATA_PREMIUM}")
        print(f"   Technical Indicators: {', '.join(cls.TECHNICAL_INDICATORS)}")
        print(f"   Stocks Enabled: {cls.ENABLE_STOCKS}")
        print(f"   FX Enabled: {cls.ENABLE_FX}")
        print(f"   Scalping Mode: {cls.SCALPING_MODE}")
        if cls.ENABLE_STOCKS:
            print(f"   Stock Symbols: {', '.join(cls.STOCK_SYMBOLS)}")
        if cls.ENABLE_FX:
            print(f"   FX Symbols: {', '.join(cls.FX_SYMBOLS)}")
        print(f"   Trading Interval: {cls.get_trading_interval()}")
