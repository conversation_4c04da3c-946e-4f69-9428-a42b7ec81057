# Continuous Analysis with Trade State Management

## Overview

The AI Trading Bot now supports continuous analysis with intelligent trade state management. This feature allows you to:

1. **Continuously monitor** a chosen symbol at your selected interval (1min, 5min, etc.)
2. **Track trade states** automatically (NO_POSITION → LONG_POSITION/SHORT_POSITION → EXIT)
3. **Get contextual signals** based on your current position
4. **Monitor P&L** in real-time for active positions

## How It Works

### Trade State Flow

```
NO_POSITION
    ↓ (AI recommends BUY)
LONG_POSITION
    ↓ (AI recommends SELL/EXIT)
NO_POSITION

NO_POSITION
    ↓ (AI recommends SELL)
SHORT_POSITION
    ↓ (AI recommends BUY/EXIT)
NO_POSITION
```

### Signal Logic

- **When NO_POSITION**: AI can recommend BUY, SELL, or HOLD
- **When LONG_POSITION**: AI will recommend HOLD (continue holding) or EXIT (close position)
- **When SHORT_POSITION**: AI will recommend HOLD (continue holding) or EXIT (close position)

## Getting Started

### 1. Launch Interactive Mode

```bash
python interactive_bot.py
```

### 2. Select Continuous Analysis

When prompted, choose option **2** for "Continuous Analysis"

### 3. Choose Your Symbol

Enter any supported symbol:
- **Stocks**: AAPL, MSFT, GOOGL, etc.
- **FX Pairs**: EURUSD, GBPUSD, EUR/USD, etc.

### 4. Select Time Interval

Choose your analysis frequency:
- **1min**: For scalping (very active trading)
- **5min**: For intraday trading
- **15min**: For swing trading
- **30min**: For position trading
- **60min/1h**: For longer-term analysis

### 5. Monitor Continuous Analysis

The bot will:
- Analyze your symbol every X minutes (based on your interval)
- Show current trade state and P&L
- Display next analysis time
- Continue until you press Ctrl+C

## Example Session

```
🔄 AI Trading Bot - Continuous Analysis Mode
This mode will continuously analyze your chosen symbol and track trade states.
The bot will give BUY signals, then HOLD until it recommends EXIT.

💹 Symbol Selection
🎯 Enter symbol: EURUSD

⏰ Time Interval Selection
🎯 Select interval: 5min

🚀 Starting Continuous Analysis
📊 Symbol: EURUSD
⏰ Interval: 5min (5 minutes)
🕐 Started: 2025-01-27 14:30:00

⏰ Analysis at 14:30:00
┌─────────────────────────────────────┐
│        Trading Signal for EURUSD   │
├─────────────────┬───────────────────┤
│ Signal          │ BUY               │
│ Confidence      │ 75.00%            │
│ Trade State     │ LONG_POSITION     │
│ Entry Price     │ 1.08450           │
│ P&L             │ +0.15%            │
└─────────────────┴───────────────────┘

📊 Current State: LONG_POSITION
⏰ Next analysis: 14:35:00
```

## Features

### Real-Time P&L Tracking

- Shows current profit/loss percentage
- Updates with each analysis
- Color-coded (green for profit, red for loss)

### Trade State Display

- **NO_POSITION**: White text
- **LONG_POSITION**: Green text
- **SHORT_POSITION**: Red text

### Smart Signal Generation

The AI considers your current position when making recommendations:
- Won't recommend conflicting signals
- Focuses on exit timing when in position
- Provides contextual reasoning

### Automatic State Management

- Tracks entry prices automatically
- Updates trade states based on signals
- Maintains position history

## Stopping the Analysis

Press **Ctrl+C** at any time to stop the continuous analysis. The bot will:
- Export all signals to a JSON file
- Display a performance summary
- Show total signals by type (BUY, SELL, HOLD, EXIT)

## Tips for Best Results

1. **Choose appropriate intervals**:
   - 1min for very active scalping
   - 5min for intraday trading
   - 15min+ for swing trading

2. **Monitor during active market hours**:
   - Stocks: 9:30 AM - 4:00 PM EST
   - FX: 24/5 (best during London/NY overlap)

3. **Use additional context**:
   - Add market conditions or news events
   - Specify your trading strategy focus

4. **Review the reasoning**:
   - Each signal includes detailed AI reasoning
   - Consider the confidence levels
   - Pay attention to validation flags

## Technical Details

### Signal Types

- **BUY**: Enter long position
- **SELL**: Enter short position
- **HOLD**: Maintain current position
- **EXIT**: Close current position

### Trade States

- **NO_POSITION**: Not currently in a trade
- **LONG_POSITION**: Currently holding a long position
- **SHORT_POSITION**: Currently holding a short position

### Data Sources

- **Stocks & FX**: Twelve Data API for historical data + real-time quotes
- **Unified Source**: Same API for all symbols and markets

## Troubleshooting

### Common Issues

1. **Analysis fails**: Check your API keys and internet connection
2. **No data**: Verify symbol format and Twelve Data API access
3. **Rate limits**: Free Twelve Data accounts have 800 API calls per day

### Error Recovery

The bot automatically handles errors by:
- Retrying failed analyses after 1 minute
- Continuing the analysis loop
- Logging all errors for debugging

## Configuration

Edit your `.env` file to customize:

```env
# Analysis intervals
SCALPING_INTERVAL=1min
INTRADAY_INTERVAL=5min

# API keys
TWELVE_DATA_API_KEY=your_key_here
OPENAI_API_KEY=your_key_here

# Trading configuration
ENABLE_STOCKS=true
ENABLE_FX=true
```
