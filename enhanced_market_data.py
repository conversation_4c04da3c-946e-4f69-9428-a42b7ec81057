"""
Enhanced Market Data Provider - Twelve Data API Integration
- Unified data source for both stocks and FX using Twelve Data API
"""
import threading
import time
from typing import Dict, Optional, List
from datetime import datetime, timedelta
import pandas as pd

from config import Config
from market_data import MarketDataProvider
from logger_config import setup_logger, log_error


class EnhancedMarketDataProvider:
    """
    Enhanced market data provider using Twelve Data API
    - Unified data source for both stocks and FX
    - Real-time quotes and historical data
    - Technical indicators support
    """

    def __init__(self):
        self.logger = setup_logger("EnhancedMarketData")

        # Initialize Twelve Data provider
        self.market_data_provider = MarketDataProvider()

        # Data cache
        self.live_data_cache: Dict[str, Dict] = {}
        self.cache_lock = threading.Lock()

    def is_connected(self) -> bool:
        """Check if Twelve Data API is accessible"""
        try:
            # Simple test call to check API connectivity
            test_quote = self.market_data_provider.get_quote('AAPL')
            return test_quote is not None
        except Exception:
            return False

    def get_live_quote(self, symbol: str) -> Optional[Dict]:
        """
        Get live quote using Twelve Data API

        Args:
            symbol: Symbol to get quote for

        Returns:
            Quote data or None if not available
        """
        # Check cache first
        with self.cache_lock:
            if symbol in self.live_data_cache:
                cache_data = self.live_data_cache[symbol]
                # Use cached data if it's recent (within 30 seconds)
                if (datetime.now() - cache_data['timestamp']).total_seconds() < 30:
                    return cache_data['quote']

        # Get fresh quote from Twelve Data API
        quote = self.market_data_provider.get_quote(symbol)

        if quote:
            # Update cache
            with self.cache_lock:
                self.live_data_cache[symbol] = {
                    'quote': quote,
                    'timestamp': datetime.now(),
                    'source': 'Twelve Data',
                    'is_live': True
                }

        return quote

    def get_comprehensive_data(self, symbol: str, interval: str = '5min') -> Dict:
        """
        Get comprehensive market data using Twelve Data API

        Args:
            symbol: Symbol to analyze
            interval: Time interval for historical data

        Returns:
            Comprehensive market data dictionary
        """
        self.logger.info(f"🔍 Fetching comprehensive data for {symbol} (Enhanced)")

        # Use the underlying market data provider
        return self.market_data_provider.get_comprehensive_data(symbol, interval)

    def get_api_statistics(self) -> Dict:
        """Get API usage statistics"""
        return {
            'data_source': 'Twelve Data',
            'api_connected': self.is_connected(),
            'cached_symbols': list(self.live_data_cache.keys()),
            'cache_size': len(self.live_data_cache)
        }

    def get_available_symbols(self) -> List[str]:
        """Get list of available symbols from configuration"""
        return Config.get_all_symbols()

    def start_live_monitoring(self, symbols: List[str] = None):
        """
        Start live monitoring for specified symbols

        Args:
            symbols: List of symbols to monitor (uses config default if None)
        """
        symbols = symbols or Config.get_all_symbols()
        self.logger.info(f"🔴 Starting live monitoring for: {', '.join(symbols)}")

        # Pre-populate cache with initial quotes
        for symbol in symbols:
            try:
                self.get_live_quote(symbol)
            except Exception as e:
                self.logger.warning(f"Failed to get initial quote for {symbol}: {e}")

    def stop_live_monitoring(self):
        """Stop live monitoring and clear cache"""
        with self.cache_lock:
            self.live_data_cache.clear()
        self.logger.info("🛑 Live monitoring stopped and cache cleared")

    def get_data_source_info(self, symbol: str) -> Dict:
        """Get information about data sources for a symbol"""
        is_fx = Config.is_fx_symbol(symbol)

        info = {
            'symbol': symbol,
            'twelve_data_available': True,
            'live_data_age_seconds': None,
            'preferred_source': 'Twelve Data',
            'is_fx': is_fx,
            'cached': False
        }

        # Check cache
        with self.cache_lock:
            if symbol in self.live_data_cache:
                cache_age = (datetime.now() - self.live_data_cache[symbol]['timestamp']).total_seconds()
                info['live_data_age_seconds'] = cache_age
                info['cached'] = True

        return info

    def force_refresh_cache(self, symbol: str) -> bool:
        """Force refresh cache for a symbol"""
        try:
            # Clear cache entry first
            with self.cache_lock:
                if symbol in self.live_data_cache:
                    del self.live_data_cache[symbol]

            # Get fresh quote
            quote = self.get_live_quote(symbol)
            return quote is not None
        except Exception as e:
            log_error(self.logger, e, f"refreshing cache for {symbol}")
            return False

    def get_cache_status(self) -> Dict:
        """Get cache status information"""
        with self.cache_lock:
            return {
                'cached_symbols': list(self.live_data_cache.keys()),
                'cache_size': len(self.live_data_cache),
                'oldest_cache_age': self._get_oldest_cache_age(),
                'newest_cache_age': self._get_newest_cache_age()
            }

    def _get_oldest_cache_age(self) -> Optional[float]:
        """Get age of oldest cache entry in seconds"""
        if not self.live_data_cache:
            return None

        oldest_time = min(data['timestamp'] for data in self.live_data_cache.values())
        return (datetime.now() - oldest_time).total_seconds()

    def _get_newest_cache_age(self) -> Optional[float]:
        """Get age of newest cache entry in seconds"""
        if not self.live_data_cache:
            return None

        newest_time = max(data['timestamp'] for data in self.live_data_cache.values())
        return (datetime.now() - newest_time).total_seconds()

    def cleanup(self):
        """Cleanup resources"""
        with self.cache_lock:
            self.live_data_cache.clear()

        self.logger.info("🧹 Enhanced market data provider cleaned up")
