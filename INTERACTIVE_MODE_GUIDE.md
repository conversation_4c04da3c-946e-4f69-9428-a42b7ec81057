# 🤖 Interactive Trading Bot Guide

## Overview
The Interactive Mode allows you to easily analyze any stock or FX pair by simply running the bot and following the prompts. No command-line arguments needed!

## 🚀 Quick Start

### Method 1: Simple Interactive Launcher
```bash
python interactive_bot.py
```

### Method 2: Using the Main Bot
```bash
python trading_bot.py --interactive
```

## 📋 How It Works

### Step 1: Symbol Selection
The bot will ask you to enter a symbol:
- **Stocks**: Enter symbols like `AAPL`, `MSFT`, `GOOGL`, `TSLA`
- **FX Pairs**: Enter pairs like `EUR/USD`, `GBP/USD`, `USD/JPY`

**Example symbols shown:**
- 📈 **Stocks**: AAPL, MSFT, GOOGL, TSLA, NVDA
- 💱 **FX Pairs**: EUR/USD, GBP/USD, USD/JPY, AUD/USD, USD/CAD

### Step 2: Time Interval Selection
Choose your analysis timeframe:

| Option | Interval | Best For |
|--------|----------|----------|
| 1 | 1min | Scalping, very short-term trades |
| 2 | 5min | Intraday trading, short-term analysis |
| 3 | 15min | Swing trading, medium-term analysis |
| 4 | 30min | Position trading, longer-term analysis |
| 5 | 60min | Daily analysis, trend following |
| 6 | 1h | Same as 60min (alternative format) |

You can also enter custom intervals like `1min`, `5min`, `15min`, etc.

### Step 3: Optional Context
Add any additional context for the AI analysis (optional):
- Market conditions you're aware of
- Specific events affecting the symbol
- Your trading strategy focus

### Step 4: Analysis Results
The bot will display:
- 📊 **Trading Signal**: BUY/SELL/HOLD with confidence level
- 💰 **Current Price**: Real-time market data
- 🎯 **Price Targets**: AI-suggested entry/exit points
- 🛡️ **Risk Assessment**: Risk level and stop-loss recommendations
- 🧠 **AI Reasoning**: Detailed explanation of the analysis
- ✅ **Execution Recommendation**: Whether to act on the signal

### Step 5: Continue or Exit
After each analysis, you can:
- Analyze another symbol (press `y`)
- Exit the bot (press `n`)

## 💡 Example Session

```
🤖 AI Trading Bot - Interactive Mode
Welcome! Let's analyze a symbol of your choice.
------------------------------------------------------------

💹 Symbol Selection
Enter a stock symbol (e.g., AAPL, MSFT) or FX pair (e.g., EUR/USD, GBP/USD)
📈 Stock examples: AAPL, MSFT, GOOGL, TSLA, NVDA
💱 FX examples: EUR/USD, GBP/USD, USD/JPY, AUD/USD, USD/CAD

🎯 Enter symbol: EUR/USD
✅ Selected symbol: EUR/USD

⏰ Time Interval Selection
┏━━━━━━━━┳━━━━━━━━━━┳━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┓
┃ Option ┃ Interval ┃ Best For                              ┃
┡━━━━━━━━╇━━━━━━━━━━╇━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━┩
│ 1      │ 1min     │ Scalping, very short-term trades     │
│ 2      │ 5min     │ Intraday trading, short-term analysis│
│ 3      │ 15min    │ Swing trading, medium-term analysis  │
│ 4      │ 30min    │ Position trading, longer-term analysis│
│ 5      │ 60min    │ Daily analysis, trend following      │
│ 6      │ 1h       │ Same as 60min (alternative format)   │
└────────┴──────────┴───────────────────────────────────────┘

🎯 Select interval (1-6) or enter custom: 2
✅ Selected interval: 5min

📝 Additional Context (Optional)
Enter any additional context for analysis: ECB meeting tomorrow

🚀 Starting Analysis
📊 Symbol: EUR/USD
⏰ Interval: 5min
🕐 Time: 2025-01-27 14:30:15
📝 Context: ECB meeting tomorrow
------------------------------------------------------------

[Analysis results displayed here...]

✅ Analysis completed successfully!

🔄 Continue Analysis?
Would you like to analyze another symbol? (y/n): n
👋 Thank you for using the AI Trading Bot!
```

## 🛡️ Input Validation

The bot includes smart validation:
- **Symbol format**: Ensures proper FX pair format (XXX/YYY)
- **Interval validation**: Only accepts valid timeframes
- **Error handling**: Graceful recovery from input errors
- **Keyboard interrupt**: Clean exit with Ctrl+C

## 🎯 Benefits of Interactive Mode

1. **User-Friendly**: No need to remember command-line arguments
2. **Flexible**: Analyze any symbol with any timeframe
3. **Educational**: Shows available options and examples
4. **Safe**: Input validation prevents errors
5. **Continuous**: Analyze multiple symbols in one session
6. **Context-Aware**: Add market context for better analysis

## 🔧 Configuration

Make sure your `.env` file is set up with:
```bash
TWELVE_DATA_API_KEY=your_actual_key_here
OPENAI_API_KEY=your_actual_key_here
```

The interactive mode will work with any symbols, regardless of your configured symbol lists in the `.env` file.
