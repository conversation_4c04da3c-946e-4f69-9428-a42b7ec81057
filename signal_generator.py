"""
Signal Generator Module - Convert AI Analysis to Trading Signals
"""
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from enum import Enum
import json
from logger_config import setup_logger, log_trade_signal

class SignalType(Enum):
    """Trading signal types"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"
    EXIT = "EXIT"

class RiskLevel(Enum):
    """Risk assessment levels"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"

class TradeState(Enum):
    """Current trade position states"""
    NO_POSITION = "NO_POSITION"
    LONG_POSITION = "LONG_POSITION"
    SHORT_POSITION = "SHORT_POSITION"

class TradingSignal:
    """Trading signal data structure"""

    def __init__(self,
                 symbol: str,
                 signal: SignalType,
                 confidence: float,
                 reasoning: str,
                 timestamp: datetime = None,
                 price_target: float = None,
                 stop_loss: float = None,
                 risk_level: RiskLevel = RiskLevel.MEDIUM,
                 time_horizon: str = "SHORT",
                 validation_flags: List[str] = None,
                 current_trade_state: TradeState = TradeState.NO_POSITION,
                 entry_price: float = None):

        self.symbol = symbol
        self.signal = signal
        self.confidence = confidence
        self.reasoning = reasoning
        self.timestamp = timestamp or datetime.now()
        self.price_target = price_target
        self.stop_loss = stop_loss
        self.risk_level = risk_level
        self.time_horizon = time_horizon
        self.validation_flags = validation_flags or []
        self.current_trade_state = current_trade_state
        self.entry_price = entry_price

    def to_dict(self) -> Dict:
        """Convert signal to dictionary"""
        return {
            'symbol': self.symbol,
            'signal': self.signal.value,
            'confidence': self.confidence,
            'reasoning': self.reasoning,
            'timestamp': self.timestamp.isoformat(),
            'price_target': self.price_target,
            'stop_loss': self.stop_loss,
            'risk_level': self.risk_level.value,
            'time_horizon': self.time_horizon,
            'validation_flags': self.validation_flags,
            'current_trade_state': self.current_trade_state.value,
            'entry_price': self.entry_price
        }

    def __str__(self) -> str:
        """String representation of the signal"""
        return f"{self.signal.value} {self.symbol} (Confidence: {self.confidence:.2%})"

class SignalGenerator:
    """Generate and manage trading signals"""

    def __init__(self):
        self.logger = setup_logger("SignalGenerator")
        self.signal_history: List[TradingSignal] = []
        # Track current trade states per symbol
        self.current_trade_states: Dict[str, TradeState] = {}
        self.entry_prices: Dict[str, float] = {}

    def get_current_trade_state(self, symbol: str) -> TradeState:
        """Get current trade state for a symbol"""
        return self.current_trade_states.get(symbol, TradeState.NO_POSITION)

    def update_trade_state(self, symbol: str, new_state: TradeState, entry_price: float = None):
        """Update trade state for a symbol"""
        old_state = self.get_current_trade_state(symbol)
        self.current_trade_states[symbol] = new_state

        if entry_price is not None:
            self.entry_prices[symbol] = entry_price
        elif new_state == TradeState.NO_POSITION:
            # Clear entry price when exiting position
            self.entry_prices.pop(symbol, None)

        self.logger.info(f"[STATE_CHANGE] {symbol}: {old_state.value} → {new_state.value}")

    def determine_signal_based_on_state(self, ai_signal: str, symbol: str, current_price: float) -> SignalType:
        """
        Determine the actual signal based on current trade state and AI recommendation

        Args:
            ai_signal: Raw AI signal (BUY/SELL/HOLD)
            symbol: Trading symbol
            current_price: Current market price

        Returns:
            Adjusted signal based on current position
        """
        current_state = self.get_current_trade_state(symbol)
        ai_signal_type = SignalType(ai_signal.upper()) if ai_signal.upper() in [s.value for s in SignalType] else SignalType.HOLD

        # If no position, follow AI recommendation
        if current_state == TradeState.NO_POSITION:
            if ai_signal_type in [SignalType.BUY, SignalType.SELL]:
                return ai_signal_type
            return SignalType.HOLD

        # If in a long position
        elif current_state == TradeState.LONG_POSITION:
            if ai_signal_type == SignalType.SELL or ai_signal_type == SignalType.EXIT:
                return SignalType.EXIT
            return SignalType.HOLD

        # If in a short position
        elif current_state == TradeState.SHORT_POSITION:
            if ai_signal_type == SignalType.BUY or ai_signal_type == SignalType.EXIT:
                return SignalType.EXIT
            return SignalType.HOLD

        return SignalType.HOLD

    def generate_signal(self, ai_analysis: Dict, market_data: Dict) -> Optional[TradingSignal]:
        """
        Generate a trading signal from AI analysis

        Args:
            ai_analysis: AI analysis result
            market_data: Market data used for analysis

        Returns:
            TradingSignal object or None if invalid
        """
        try:
            # Extract signal information
            signal_str = ai_analysis.get('signal', 'HOLD').upper()
            confidence = ai_analysis.get('adjusted_confidence', ai_analysis.get('confidence', 0.0))
            reasoning = ai_analysis.get('reasoning', 'No reasoning provided')

            # Get symbol and current price from market data
            symbol = market_data.get('symbol', 'UNKNOWN')
            current_price = market_data.get('quote', {}).get('price', 0.0)

            # Determine actual signal based on current trade state
            actual_signal = self.determine_signal_based_on_state(signal_str, symbol, current_price)

            # Update reasoning to include trade state context
            current_state = self.get_current_trade_state(symbol)
            if current_state != TradeState.NO_POSITION:
                entry_price = self.entry_prices.get(symbol, 0.0)
                if entry_price > 0:
                    pnl_pct = ((current_price - entry_price) / entry_price) * 100
                    if current_state == TradeState.SHORT_POSITION:
                        pnl_pct = -pnl_pct
                    reasoning += f"\n\nCurrent Position: {current_state.value} at {entry_price:.5f}. Current P&L: {pnl_pct:+.2f}%"

            # Extract additional information
            price_target = ai_analysis.get('price_target')
            stop_loss = ai_analysis.get('stop_loss')
            time_horizon = ai_analysis.get('time_horizon', 'SHORT')
            validation_flags = ai_analysis.get('validation_flags', [])

            # Determine risk level
            risk_level_str = ai_analysis.get('risk_assessment', 'MEDIUM').upper()
            try:
                risk_level = RiskLevel(risk_level_str)
            except ValueError:
                risk_level = RiskLevel.MEDIUM

            # Create trading signal with state information
            trading_signal = TradingSignal(
                symbol=symbol,
                signal=actual_signal,
                confidence=confidence,
                reasoning=reasoning,
                price_target=price_target,
                stop_loss=stop_loss,
                risk_level=risk_level,
                time_horizon=time_horizon,
                validation_flags=validation_flags,
                current_trade_state=current_state,
                entry_price=self.entry_prices.get(symbol)
            )

            # Update trade state based on signal
            if actual_signal == SignalType.BUY and current_state == TradeState.NO_POSITION:
                self.update_trade_state(symbol, TradeState.LONG_POSITION, current_price)
            elif actual_signal == SignalType.SELL and current_state == TradeState.NO_POSITION:
                self.update_trade_state(symbol, TradeState.SHORT_POSITION, current_price)
            elif actual_signal == SignalType.EXIT:
                self.update_trade_state(symbol, TradeState.NO_POSITION)

            # Add to history
            self.signal_history.append(trading_signal)

            # Log the signal
            log_trade_signal(
                self.logger,
                symbol,
                actual_signal.value,
                confidence,
                reasoning
            )

            return trading_signal

        except Exception as e:
            self.logger.error(f"Error generating signal: {e}")
            return None

    def should_execute_signal(self, signal: TradingSignal, min_confidence: float = 0.7) -> bool:
        """
        Determine if a signal should be executed based on criteria

        Args:
            signal: Trading signal to evaluate
            min_confidence: Minimum confidence threshold

        Returns:
            True if signal should be executed
        """
        # Check confidence threshold
        if signal.confidence < min_confidence:
            self.logger.info(f"[REJECT] Signal confidence {signal.confidence:.2%} below threshold {min_confidence:.2%}")
            return False

        # Check for blocking validation flags
        blocking_flags = ['EXTREME_MOVEMENT', 'LOW_VOLUME']
        if any(flag in signal.validation_flags for flag in blocking_flags):
            self.logger.info(f"[REJECT] Signal blocked by validation flags: {signal.validation_flags}")
            return False

        # Don't execute HOLD signals
        if signal.signal == SignalType.HOLD:
            self.logger.info("[INFO] HOLD signal - no action required")
            return False

        # Always execute EXIT signals regardless of confidence
        if signal.signal == SignalType.EXIT:
            self.logger.info(f"[APPROVE] EXIT signal approved for execution: {signal}")
            return True

        self.logger.info(f"[APPROVE] Signal approved for execution: {signal}")
        return True

    def get_recent_signals(self, symbol: str = None, hours: int = 24) -> List[TradingSignal]:
        """
        Get recent signals for a symbol or all symbols

        Args:
            symbol: Stock symbol (optional)
            hours: Number of hours to look back

        Returns:
            List of recent trading signals
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)

        recent_signals = [
            signal for signal in self.signal_history
            if signal.timestamp >= cutoff_time
        ]

        if symbol:
            recent_signals = [
                signal for signal in recent_signals
                if signal.symbol == symbol
            ]

        return recent_signals

    def get_signal_summary(self, symbol: str = None) -> Dict:
        """
        Get summary statistics for signals

        Args:
            symbol: Stock symbol (optional)

        Returns:
            Dictionary with signal statistics
        """
        signals = self.signal_history
        if symbol:
            signals = [s for s in signals if s.symbol == symbol]

        if not signals:
            return {'total_signals': 0}

        buy_signals = [s for s in signals if s.signal == SignalType.BUY]
        sell_signals = [s for s in signals if s.signal == SignalType.SELL]
        hold_signals = [s for s in signals if s.signal == SignalType.HOLD]
        exit_signals = [s for s in signals if s.signal == SignalType.EXIT]

        avg_confidence = sum(s.confidence for s in signals) / len(signals)

        return {
            'total_signals': len(signals),
            'buy_signals': len(buy_signals),
            'sell_signals': len(sell_signals),
            'hold_signals': len(hold_signals),
            'exit_signals': len(exit_signals),
            'average_confidence': avg_confidence,
            'latest_signal': signals[-1].to_dict() if signals else None
        }

    def export_signals(self, filename: str = None) -> str:
        """
        Export signal history to JSON file

        Args:
            filename: Output filename (optional)

        Returns:
            Filename of exported file
        """
        if not filename:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"trading_signals_{timestamp}.json"

        signals_data = [signal.to_dict() for signal in self.signal_history]

        with open(filename, 'w') as f:
            json.dump(signals_data, f, indent=2)

        self.logger.info(f"[EXPORT] Exported {len(signals_data)} signals to {filename}")
        return filename
