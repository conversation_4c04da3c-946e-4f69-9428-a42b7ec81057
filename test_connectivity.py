#!/usr/bin/env python3
"""
Connectivity Test Script
Tests internet connection, API connectivity, and API key validity
"""
import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from market_data import MarketDataProvider
from config import Config
from rich.console import Console

def test_connectivity():
    """Test all connectivity aspects"""
    console = Console()
    
    console.print("\n[bold blue]Trading Bot Connectivity Test[/bold blue]")
    console.print("=" * 50)
    
    # Initialize market data provider
    provider = MarketDataProvider()
    
    # Test 1: Internet Connection
    console.print("\n[cyan]1. Testing Internet Connection...[/cyan]")
    if provider.check_internet_connection():
        console.print("[green]✓ Internet connection is working[/green]")
    else:
        console.print("[red]✗ No internet connection detected[/red]")
        console.print("[yellow]Please check your network connection[/yellow]")
        return False
    
    # Test 2: API Connectivity
    console.print("\n[cyan]2. Testing Twelve Data API Connectivity...[/cyan]")
    if provider.check_api_connectivity():
        console.print("[green]✓ Twelve Data API is reachable[/green]")
    else:
        console.print("[red]✗ Cannot reach Twelve Data API[/red]")
        console.print("[yellow]The API service may be down or blocked[/yellow]")
        return False
    
    # Test 3: API Key Configuration
    console.print("\n[cyan]3. Testing API Key Configuration...[/cyan]")
    if Config.TWELVE_DATA_API_KEY:
        console.print(f"[green]✓ API key is configured[/green]")
        console.print(f"[dim]Key: {Config.TWELVE_DATA_API_KEY[:8]}...{Config.TWELVE_DATA_API_KEY[-4:]}[/dim]")
    else:
        console.print("[red]✗ No API key found[/red]")
        console.print("[yellow]Please set TWELVE_DATA_API_KEY in your .env file[/yellow]")
        return False
    
    # Test 4: API Key Validity
    console.print("\n[cyan]4. Testing API Key Validity...[/cyan]")
    try:
        test_quote = provider.get_quote("AAPL")
        if test_quote:
            console.print("[green]✓ API key is valid and working[/green]")
            console.print(f"[dim]Test quote for AAPL: ${test_quote['price']:.2f}[/dim]")
        else:
            console.print("[red]✗ API key test failed[/red]")
            console.print("[yellow]The API key may be invalid or rate limited[/yellow]")
            return False
    except Exception as e:
        console.print(f"[red]✗ API test error: {str(e)}[/red]")
        return False
    
    # Test 5: FX Symbol Test
    console.print("\n[cyan]5. Testing FX Symbol Access...[/cyan]")
    try:
        fx_quote = provider.get_quote("USD/CAD")
        if fx_quote:
            console.print("[green]✓ FX symbols are accessible[/green]")
            console.print(f"[dim]Test quote for USD/CAD: {fx_quote['price']:.5f}[/dim]")
        else:
            console.print("[yellow]⚠ FX symbols may not be available with your API plan[/yellow]")
    except Exception as e:
        console.print(f"[yellow]⚠ FX test warning: {str(e)}[/yellow]")
    
    console.print("\n[green]✓ All connectivity tests passed![/green]")
    console.print("[green]Your trading bot should work properly.[/green]")
    return True

def main():
    """Main function"""
    try:
        success = test_connectivity()
        if success:
            print("\n" + "="*50)
            print("CONNECTIVITY TEST: PASSED")
            print("Your trading bot is ready to use!")
        else:
            print("\n" + "="*50)
            print("CONNECTIVITY TEST: FAILED")
            print("Please fix the issues above before running the trading bot.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nTest cancelled by user.")
        sys.exit(0)
    except Exception as e:
        print(f"\nUnexpected error during connectivity test: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
