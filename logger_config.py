"""
Logging configuration for the Trading Bot
"""
import logging
import sys
from datetime import datetime
from pathlib import Path
from rich.logging import RichHandler
from rich.console import Console
from config import Config

def setup_logger(name: str = "TradingBot") -> logging.Logger:
    """
    Set up a logger with both file and console handlers

    Args:
        name: Logger name

    Returns:
        Configured logger instance
    """

    # Create logs directory if it doesn't exist
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))

    # Clear existing handlers
    logger.handlers.clear()

    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s | %(name)s | %(levelname)s | %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # File handler with UTF-8 encoding
    log_filename = logs_dir / f"trading_bot_{datetime.now().strftime('%Y%m%d')}.log"
    file_handler = logging.FileHandler(log_filename, encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    # Rich console handler for beautiful terminal output
    console = Console()
    rich_handler = RichHandler(
        console=console,
        show_time=True,
        show_path=False,
        rich_tracebacks=True
    )
    rich_handler.setLevel(getattr(logging, Config.LOG_LEVEL.upper()))
    logger.addHandler(rich_handler)

    return logger

def log_trade_signal(logger: logging.Logger, symbol: str, signal: str, confidence: float, reasoning: str):
    """
    Log a trading signal with structured format

    Args:
        logger: Logger instance
        symbol: Stock symbol
        signal: BUY/SELL/HOLD
        confidence: Confidence level (0-1)
        reasoning: AI reasoning for the signal
    """
    logger.info(f"[SIGNAL] {signal} | {symbol} | Confidence: {confidence:.2%}")
    logger.info(f"[REASONING] {reasoning}")

def log_market_data(logger: logging.Logger, symbol: str, price: float, change: float):
    """
    Log market data update

    Args:
        logger: Logger instance
        symbol: Stock symbol
        price: Current price
        change: Price change percentage
    """
    direction = "[UP]" if change >= 0 else "[DOWN]"
    logger.info(f"{direction} {symbol}: ${price:.2f} ({change:+.2%})")

def log_api_call(logger: logging.Logger, api_name: str, endpoint: str, status: str):
    """
    Log API call information

    Args:
        logger: Logger instance
        api_name: Name of the API (Alpha Vantage, OpenAI)
        endpoint: API endpoint called
        status: Success/Error status
    """
    status_indicator = "[OK]" if status == "success" else "[ERROR]"
    logger.debug(f"{status_indicator} {api_name} API: {endpoint} - {status}")

def log_error(logger: logging.Logger, error: Exception, context: str = ""):
    """
    Log error with context

    Args:
        logger: Logger instance
        error: Exception that occurred
        context: Additional context about where the error occurred
    """
    context_str = f" in {context}" if context else ""
    logger.error(f"[ERROR]{context_str}: {str(error)}", exc_info=True)
